# Solución para Pantalla en Blanco en Electron

## 🔧 Cambios Realizados

### 1. **Corregido index.html**
- ❌ Removido `importmap` que causaba conflictos con Electron
- ❌ Removidas referencias a ESM externos
- ✅ Configuración limpia para Vite + Electron

### 2. **Mejorado main.js**
- ✅ Mejor detección de modo desarrollo vs producción
- ✅ Manejo de errores mejorado
- ✅ Logging detallado para debugging
- ✅ `webSecurity: false` para desarrollo
- ✅ Eventos de debugging adicionales

### 3. **Scripts Mejorados**
- ✅ `electron-dev.js` - Script personalizado más robusto
- ✅ Mejor manejo de la secuencia de inicio
- ✅ Verificación de que el servidor esté listo antes de abrir Electron

### 4. **Package.json Actualizado**
- ✅ Agregado `cross-env` para variables de entorno
- ✅ Scripts adicionales para debugging
- ✅ `--host` en Vite para mejor acceso desde Electron

## 🚀 Cómo Usar Ahora

### Paso 1: Instalar dependencias
```bash
npm install
```

### Paso 2: Ejecutar en modo desarrollo
```bash
npm run electron-dev
```

### Alternativas si hay problemas:
```bash
# Usar el método anterior
npm run electron-dev-old

# Solo para debugging de Electron
npm run test-electron

# Con debugging avanzado
npm run electron-dev-debug
```

## 🔍 Debugging

### Si aún aparece pantalla en blanco:

1. **Verificar que Vite esté corriendo:**
   - Abre http://localhost:5173 en tu navegador
   - Debe mostrar la aplicación correctamente

2. **Verificar logs de Electron:**
   - Busca mensajes en la consola como:
     - "Electron window created"
     - "Window ready to show"
     - "Page finished loading"

3. **Abrir DevTools en Electron:**
   - Las DevTools se abren automáticamente en desarrollo
   - Busca errores en la consola

### Errores Comunes y Soluciones:

#### Error: "Failed to load resource"
```bash
# Asegúrate de que Vite esté corriendo primero
npm run dev
# En otra terminal:
npm run electron
```

#### Error: "Cannot read package.json"
```bash
# Verifica que estés en el directorio correcto
pwd
ls package.json
```

#### Error: "Port 5173 already in use"
```bash
# Mata el proceso que usa el puerto
npx kill-port 5173
# O cambia el puerto en vite.config.ts
```

## 📋 Checklist de Verificación

- [ ] ✅ `npm install` completado sin errores
- [ ] ✅ `package.json` existe y tiene los scripts correctos
- [ ] ✅ `main.js` y `preload.js` están presentes
- [ ] ✅ `index.html` no tiene importmaps
- [ ] ✅ Puerto 5173 está disponible
- [ ] ✅ No hay otros procesos de Electron corriendo

## 🛠️ Comandos de Diagnóstico

```bash
# Verificar que Vite funciona solo
npm run dev

# Verificar que Electron funciona con archivo local
npm run build
npm run electron

# Ver procesos corriendo en puerto 5173
netstat -ano | findstr :5173

# Matar procesos de Electron
taskkill /f /im electron.exe
```

## 📝 Notas Importantes

1. **Orden de inicio**: Vite DEBE estar corriendo antes que Electron
2. **Puerto fijo**: La aplicación usa puerto 5173 por defecto
3. **Variables de entorno**: `NODE_ENV=development` es crucial
4. **DevTools**: Se abren automáticamente en desarrollo para debugging

## 🎯 Si Nada Funciona

1. **Reinicia completamente:**
   ```bash
   # Mata todos los procesos
   taskkill /f /im electron.exe
   taskkill /f /im node.exe
   
   # Limpia y reinstala
   rm -rf node_modules package-lock.json
   npm install
   
   # Intenta de nuevo
   npm run electron-dev
   ```

2. **Verifica manualmente:**
   ```bash
   # Terminal 1: Solo Vite
   npm run dev
   
   # Terminal 2: Solo Electron (después de que Vite esté listo)
   npm run test-electron
   ```

La aplicación ahora debería cargar correctamente sin pantalla en blanco. Los logs te ayudarán a identificar cualquier problema restante.
