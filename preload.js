const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Exponer APIs seguras al proceso de renderizado
contextBridge.exposeInMainWorld('electronAPI', {
    // Función para obtener la dirección MAC
    getMacAddress: () => ipcRenderer.invoke('get-mac-address'),
    
    // Información del entorno
    isElectron: true,
    platform: process.platform,
    
    // Funciones adicionales que podrías necesitar en el futuro
    getVersion: () => process.versions.electron,
    
    // Función para verificar si estamos en modo desarrollo
    isDevelopment: () => process.env.NODE_ENV === 'development'
});

// Prevenir que el proceso de renderizado acceda a APIs peligrosas
delete window.require;
delete window.exports;
delete window.module;
