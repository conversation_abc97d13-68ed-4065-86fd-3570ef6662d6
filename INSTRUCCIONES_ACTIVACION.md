# Instrucciones de Activación - Strategy Creator

## ✅ Cambios Implementados

### 1. Verificación por MAC de Red (Electron)

La aplicación ahora utiliza la dirección MAC real del equipo para generar el identificador de máquina cuando se ejecuta en Electron.

**Archivos modificados:**
- `App.tsx` - Lógica de activación actualizada
- `main.js` - Proceso principal de Electron (NUEVO)
- `preload.js` - Script de precarga seguro (NUEVO)
- `package.json` - Dependencias y scripts de Electron

### 2. Verificación Continua de Activación

La aplicación ahora verifica constantemente la validez de la clave de activación:

- **Cada 30 segundos** durante la ejecución
- **Al regresar el foco** a la ventana
- **Al cambiar la visibilidad** de la pestaña/ventana
- **Verificación doble** durante el proceso de activación

### 3. Seguridad Mejorada

- Context isolation habilitado
- Node integration deshabilitado en el renderer
- Prevención de navegación a URLs externas
- Content Security Policy configurado
- APIs expuestas de forma segura

## 🚀 Cómo Usar

### Para Desarrollo:

```bash
# Instalar dependencias (incluye Electron)
npm install

# Ejecutar en modo desarrollo (Vite + Electron)
npm run electron-dev
```

### Para Producción:

```bash
# Construir y empaquetar la aplicación
npm run dist
```

Esto creará los instaladores en la carpeta `dist-electron/`.

### Para Solo Web (sin Electron):

```bash
# Desarrollo web normal
npm run dev

# Build web normal
npm run build
```

## 🔧 Funcionamiento de la Activación

### En Electron:
1. Se obtiene la MAC address real del equipo
2. Se genera la clave basada en esta MAC
3. Verificación continua cada 30 segundos

### En Navegador Web:
1. Se genera un UUID persistente (fallback)
2. Se almacena en localStorage
3. Misma verificación continua

## 📋 Generación de Claves

Para generar una clave de activación válida:

1. Obtén la MAC address del equipo del cliente
2. Usa el algoritmo HMAC-SHA256 con la clave secreta
3. Toma los primeros 5 caracteres + últimos 7 caracteres del hash
4. La clave resultante debe tener exactamente 12 caracteres

**Ejemplo de código para generar claves:**

```javascript
const crypto = require('crypto');

function generateActivationKey(macAddress) {
    const secret = 'DepredadorxDStrategy77'; // Tu clave secreta
    const hash = crypto.createHmac('sha256', secret)
                      .update(macAddress)
                      .digest('hex');
    return hash.substring(0, 5) + hash.slice(-7);
}

// Ejemplo de uso:
const macAddress = '00:11:22:33:44:55';
const key = generateActivationKey(macAddress);
console.log(`Clave para ${macAddress}: ${key}`);
```

## 🛡️ Características de Seguridad

### Verificación Robusta:
- La clave se verifica múltiples veces durante la activación
- Verificación continua en segundo plano
- Desactivación automática si la clave se vuelve inválida

### Protección Electron:
- Proceso de renderizado aislado
- APIs limitadas y seguras
- Prevención de inyección de código
- Navegación restringida

## 📁 Archivos Importantes

- `main.js` - Proceso principal de Electron
- `preload.js` - Script de precarga seguro
- `electron-security.js` - Configuraciones de seguridad
- `App.tsx` - Lógica de activación (líneas 283-381)
- `components/ActivationModal.tsx` - Modal de activación mejorado

## 🔍 Debugging

Para verificar que todo funciona:

1. **En desarrollo**: Las DevTools están disponibles
2. **Verificar MAC**: Revisa la consola para ver la MAC obtenida
3. **Verificar activación**: Los logs muestran el estado de verificación

## ⚠️ Notas Importantes

1. **Electron vs Web**: La aplicación detecta automáticamente si está en Electron
2. **Fallback**: Si no puede obtener la MAC, usa UUID como respaldo
3. **Persistencia**: Las claves se almacenan en localStorage
4. **Seguridad**: La verificación es continua y robusta

## 🎯 Próximos Pasos

1. Instalar dependencias: `npm install`
2. Probar en desarrollo: `npm run electron-dev`
3. Generar claves para tus clientes usando sus MAC addresses
4. Distribuir la aplicación empaquetada: `npm run dist`

La aplicación ahora está completamente configurada para usar verificación por MAC de red en Electron con verificación continua de la activación.
