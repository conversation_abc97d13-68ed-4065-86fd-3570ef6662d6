const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { networkInterfaces } = require('os');
const { setupSecurity } = require('./electron-security');

// Función para obtener la dirección MAC
function getMacAddress() {
    try {
        const nets = networkInterfaces();
        const macAddress = Object.values(nets)
            .flat()
            .find(net => net && !net.internal && net.mac && net.mac !== '00:00:00:00:00:00')?.mac;
        
        return macAddress || null;
    } catch (error) {
        console.error('Error getting MAC address:', error);
        return null;
    }
}

// Variable para almacenar la ventana principal
let mainWindow;

function createWindow() {
    // Crear la ventana del navegador
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false, // Por seguridad
            contextIsolation: true, // Por seguridad
            preload: path.join(__dirname, 'preload.js'), // Script de precarga
            webSecurity: true
        },
        icon: path.join(__dirname, 'assets/icon.png'), // Opcional: icono de la aplicación
        show: false // No mostrar hasta que esté listo
    });

    // Cargar la aplicación
    if (process.env.NODE_ENV === 'development') {
        // En desarrollo, cargar desde el servidor de desarrollo
        mainWindow.loadURL('http://localhost:5173');
        // Abrir las herramientas de desarrollo en modo desarrollo
        mainWindow.webContents.openDevTools();
    } else {
        // En producción, cargar el archivo HTML compilado
        mainWindow.loadFile(path.join(__dirname, 'dist/index.html'));
    }

    // Mostrar la ventana cuando esté lista
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // Manejar el cierre de la ventana
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// Este método se llamará cuando Electron haya terminado de inicializarse
app.whenReady().then(() => {
    // Configurar seguridad antes de crear ventanas
    setupSecurity();

    createWindow();

    app.on('activate', () => {
        // En macOS, es común recrear una ventana en la aplicación cuando
        // se hace clic en el icono del dock y no hay otras ventanas abiertas.
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

// Salir cuando todas las ventanas estén cerradas, excepto en macOS
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Manejar la solicitud de dirección MAC desde el proceso de renderizado
ipcMain.handle('get-mac-address', () => {
    return getMacAddress();
});

// Prevenir la navegación a URLs externas por seguridad
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
    });

    contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        // Permitir solo navegación local en desarrollo
        if (process.env.NODE_ENV === 'development' && parsedUrl.origin === 'http://localhost:5173') {
            return;
        }
        
        // Prevenir navegación a URLs externas
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });
});

// Configuración adicional de seguridad
app.on('web-contents-created', (event, contents) => {
    contents.on('will-attach-webview', (event, webPreferences, params) => {
        // Eliminar preload scripts si no son nuestros
        delete webPreferences.preload;
        delete webPreferences.preloadURL;

        // Deshabilitar node integration
        webPreferences.nodeIntegration = false;
    });
});
