// Script de desarrollo para Electron con mejor manejo de errores
const { spawn } = require('child_process');
const { createServer } = require('vite');
const electron = require('electron');
const path = require('path');

let electronProcess = null;
let viteServer = null;

async function startViteServer() {
    console.log('🚀 Iniciando servidor Vite...');
    
    try {
        viteServer = await createServer({
            server: {
                port: 5173,
                host: 'localhost'
            }
        });
        
        await viteServer.listen();
        console.log('✅ Servidor Vite iniciado en http://localhost:5173');
        return true;
    } catch (error) {
        console.error('❌ Error iniciando servidor Vite:', error);
        return false;
    }
}

function startElectron() {
    console.log('🔧 Iniciando Electron...');
    
    electronProcess = spawn(electron, ['.'], {
        env: {
            ...process.env,
            NODE_ENV: 'development'
        },
        stdio: 'inherit'
    });

    electronProcess.on('close', (code) => {
        console.log(`📱 Electron cerrado con código: ${code}`);
        process.exit(0);
    });

    electronProcess.on('error', (error) => {
        console.error('❌ Error en Electron:', error);
    });
}

async function waitForServer(url, timeout = 30000) {
    const start = Date.now();
    
    while (Date.now() - start < timeout) {
        try {
            const response = await fetch(url);
            if (response.ok) {
                return true;
            }
        } catch (error) {
            // Servidor aún no está listo
        }
        
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    return false;
}

async function main() {
    console.log('🎯 Iniciando desarrollo de Electron...');
    
    // Iniciar servidor Vite
    const viteStarted = await startViteServer();
    if (!viteStarted) {
        console.error('❌ No se pudo iniciar el servidor Vite');
        process.exit(1);
    }
    
    // Esperar a que el servidor esté disponible
    console.log('⏳ Esperando que el servidor esté disponible...');
    const serverReady = await waitForServer('http://localhost:5173');
    
    if (!serverReady) {
        console.error('❌ El servidor no está disponible después de 30 segundos');
        process.exit(1);
    }
    
    console.log('✅ Servidor disponible, iniciando Electron...');
    
    // Pequeña pausa adicional para asegurar que todo esté listo
    setTimeout(() => {
        startElectron();
    }, 1000);
}

// Manejo de cierre limpio
process.on('SIGINT', () => {
    console.log('\n🛑 Cerrando aplicación...');
    
    if (electronProcess) {
        electronProcess.kill();
    }
    
    if (viteServer) {
        viteServer.close();
    }
    
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Terminando aplicación...');
    
    if (electronProcess) {
        electronProcess.kill();
    }
    
    if (viteServer) {
        viteServer.close();
    }
    
    process.exit(0);
});

// Iniciar
main().catch(error => {
    console.error('❌ Error fatal:', error);
    process.exit(1);
});
