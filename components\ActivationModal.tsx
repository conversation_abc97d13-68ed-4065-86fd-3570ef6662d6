import React, { useState } from 'react';

interface ActivationModalProps {
    machineId: string;
    onActivationSuccess: (key: string) => void;
    t: (key: string) => string;
    verifyKey: (machineId: string, activationKey: string) => Promise<boolean>;
}

export const ActivationModal: React.FC<ActivationModalProps> = ({ machineId, onActivationSuccess, t, verifyKey }) => {
    const [key, setKey] = useState('');
    const [error, setError] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);

    const handleKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        // Solo permitir letras y números, máximo 12 caracteres
        const cleaned = e.target.value.replace(/[^A-Z0-9]/g, '').substring(0, 12);
        setKey(cleaned.toUpperCase());
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (key.length !== 12 || isVerifying) return;

        setIsVerifying(true);
        setError('');

        try {
            // Verificar la clave dos veces para asegurar consistencia
            const isValid1 = await verifyKey(machineId, key);
            const isValid2 = await verifyKey(machineId, key);

            if (isValid1 && isValid2) {
                // Verificación adicional: asegurar que la clave funciona después de almacenarla
                localStorage.setItem('activationKey', key);
                const finalCheck = await verifyKey(machineId, key);

                if (finalCheck) {
                    onActivationSuccess(key);
                } else {
                    localStorage.removeItem('activationKey');
                    setError(t('activation_invalid_key'));
                    setIsVerifying(false);
                }
            } else {
                setError(t('activation_invalid_key'));
                setIsVerifying(false);
            }
        } catch (error) {
            console.error('Error during activation verification:', error);
            setError(t('activation_invalid_key'));
            setIsVerifying(false);
        }
    };
    
    return (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: '#111827', // gray-900
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 50,
          padding: '16px',
          fontFamily: 'system-ui, -apple-system, sans-serif'
        }}>
          <div style={{
            width: '100%',
            maxWidth: '400px',
            margin: '0 auto'
          }}>
            {/* Contenedor principal con tema del proyecto */}
            <div style={{
              backgroundColor: '#1f2937', // gray-800
              padding: '32px',
              borderRadius: '8px',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              border: '1px solid #374151' // gray-700
            }}>

              {/* Header */}
              <div style={{ textAlign: 'center', marginBottom: '32px' }}>
                <h1 style={{
                  fontSize: '24px',
                  fontWeight: '600',
                  color: 'white',
                  marginBottom: '16px',
                  margin: '0 0 16px 0'
                }}>{t('activation_title')}</h1>
                <p style={{
                  color: '#d1d5db', // gray-300
                  fontSize: '14px',
                  lineHeight: '1.6',
                  margin: 0
                }}>{t('activation_description')}</p>
              </div>

              {/* Sección ID de Máquina */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  color: 'white',
                  fontWeight: '500',
                  marginBottom: '12px'
                }}>{t('activation_mac_label')}</label>
                <div style={{
                  backgroundColor: '#374151', // gray-700
                  padding: '12px',
                  borderRadius: '4px',
                  border: '1px solid #4b5563' // gray-600
                }}>
                  <div style={{
                    fontFamily: 'monospace',
                    color: '#e5e7eb', // gray-200
                    fontSize: '12px',
                    letterSpacing: '0.05em',
                    wordBreak: 'break-all',
                    userSelect: 'all',
                    textAlign: 'center'
                  }}>
                    {machineId}
                  </div>
                </div>
              </div>

              {/* Formulario de activación */}
              <form onSubmit={handleSubmit}>
                <div style={{ marginBottom: '24px' }}>
                  <label htmlFor="activation-key" style={{
                    display: 'block',
                    color: 'white',
                    fontWeight: '500',
                    marginBottom: '12px'
                  }}>{t('activation_key_label')}</label>
                  <input
                    id="activation-key"
                    type="text"
                    value={key}
                    onChange={handleKeyChange}
                    maxLength={12}
                    style={{
                      width: '100%',
                      backgroundColor: '#374151', // gray-700
                      padding: '12px',
                      borderRadius: '4px',
                      border: '1px solid #4b5563', // gray-600
                      textAlign: 'center',
                      fontFamily: 'monospace',
                      fontSize: '14px',
                      color: 'white',
                      textTransform: 'uppercase',
                      letterSpacing: '0.1em',
                      outline: 'none',
                      transition: 'all 0.2s'
                    }}
                    disabled={isVerifying}
                    autoFocus
                    placeholder="XXXXXXXXXXXX"
                    onFocus={(e) => {
                      e.target.style.borderColor = '#3b82f6'; // blue-500
                      e.target.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.5)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#4b5563'; // gray-600
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                </div>

                {error && (
                  <div style={{
                    backgroundColor: 'rgba(127, 29, 29, 0.5)', // red-900/50
                    border: '1px solid #b91c1c', // red-700
                    borderRadius: '4px',
                    padding: '12px',
                    textAlign: 'center',
                    marginBottom: '24px'
                  }}>
                    <p style={{
                      color: '#fecaca', // red-200
                      fontSize: '14px',
                      margin: 0
                    }}>{error}</p>
                  </div>
                )}

                <button
                  type="submit"
                  style={{
                    width: '100%',
                    backgroundColor: isVerifying || key.length !== 12 ? '#4b5563' : '#2563eb', // blue-600
                    color: 'white',
                    fontWeight: '500',
                    padding: '12px 24px',
                    borderRadius: '4px',
                    border: 'none',
                    cursor: isVerifying || key.length !== 12 ? 'not-allowed' : 'pointer',
                    opacity: isVerifying || key.length !== 12 ? 0.5 : 1,
                    transition: 'all 0.2s',
                    fontSize: '14px'
                  }}
                  disabled={isVerifying || key.length !== 12}
                  onMouseEnter={(e) => {
                    if (!isVerifying && key.length === 12) {
                      e.target.style.backgroundColor = '#1d4ed8'; // blue-700
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isVerifying && key.length === 12) {
                      e.target.style.backgroundColor = '#2563eb'; // blue-600
                    }
                  }}
                >
                  {isVerifying ? (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px'
                    }}>
                      <div style={{
                        width: '16px',
                        height: '16px',
                        border: '2px solid rgba(255, 255, 255, 0.3)',
                        borderTop: '2px solid white',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }}></div>
                      <span>{t('activation_verifying')}</span>
                    </div>
                  ) : (
                    t('activation_button')
                  )}
                </button>
              </form>

              {/* Footer */}
              <div style={{
                marginTop: '32px',
                paddingTop: '24px',
                borderTop: '1px solid #374151', // gray-700
                textAlign: 'center'
              }}>
                <p style={{
                  color: '#9ca3af', // gray-400
                  fontSize: '14px',
                  margin: 0
                }}>
                  by <span style={{
                    color: 'white',
                    fontWeight: '500'
                  }}>xDepredadorxD</span>
                </p>
              </div>

            </div>
          </div>
        </div>

        {/* Agregar estilos para la animación de spin */}
        <style>{`
          @keyframes spin {
            to {
              transform: rotate(360deg);
            }
          }
        `}</style>
    );
};
