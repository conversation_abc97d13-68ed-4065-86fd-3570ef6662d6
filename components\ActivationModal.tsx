import React, { useState } from 'react';

interface ActivationModalProps {
    machineId: string;
    onActivationSuccess: (key: string) => void;
    t: (key: string) => string;
    verifyKey: (machineId: string, activationKey: string) => Promise<boolean>;
}

export const ActivationModal: React.FC<ActivationModalProps> = ({ machineId, onActivationSuccess, t, verifyKey }) => {
    const [key, setKey] = useState('');
    const [error, setError] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (key.length !== 12 || isVerifying) return;

        setIsVerifying(true);
        setError('');

        const isValid = await verifyKey(machineId, key);
        if (isValid) {
            onActivationSuccess(key);
        } else {
            setError(t('activation_invalid_key'));
            setIsVerifying(false);
        }
    };
    
    return (
        <div className="fixed inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50 p-4 font-sans">
          <div className="w-full max-w-md p-8 rounded-2xl shadow-2xl text-white" style={{background: 'linear-gradient(145deg, #2d3748, #1a202c)'}}>
            <h1 className="text-3xl font-bold text-center text-blue-400 mb-2">{t('activation_title')}</h1>
            <p className="text-center text-gray-300 mb-6">{t('activation_description')}</p>
            
            <div className="mb-4">
              <label className="block text-sm font-bold mb-2 text-gray-400">{t('activation_mac_label')}</label>
              <div className="bg-black/30 p-3 rounded-md text-center font-mono text-sm sm:text-base tracking-widest break-all select-all">
                {machineId}
              </div>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="activation-key" className="block text-sm font-bold mb-2 text-gray-400">{t('activation_key_label')}</label>
                <input
                  id="activation-key"
                  type="text"
                  value={key}
                  onChange={(e) => setKey(e.target.value)}
                  maxLength={12}
                  className="w-full bg-black/30 p-3 rounded-md border border-white/20 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center font-mono text-xl sm:text-2xl uppercase tracking-[0.2em]"
                  disabled={isVerifying}
                  autoFocus
                  placeholder="____________"
                />
              </div>
              
              {error && <p className="text-red-500 text-center mb-4">{error}</p>}
              
              <button 
                type="submit" 
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isVerifying || key.length !== 12}
              >
                {isVerifying ? t('activation_verifying') : t('activation_button')}
              </button>
            </form>
            <div className="mt-8 text-center">
                <svg width="100%" height="24">
                    <defs>
                        <filter id="neon-glow-author-modal">
                            <feGaussianBlur in="SourceAlpha" stdDeviation="3.5" result="blur"/>
                            <feFlood floodColor="blue" result="color"/>
                            <feComposite in="color" in2="blur" operator="in" result="coloredBlur"/>
                            <feMerge>
                                <feMergeNode in="coloredBlur"/>
                                <feMergeNode in="SourceGraphic"/>
                            </feMerge>
                        </filter>
                    </defs>
                    <text
                        x="50%"
                        y="50%"
                        dy=".3em"
                        textAnchor="middle"
                        fontSize="14"
                        fill="lightblue"
                        filter="url(#neon-glow-author-modal)"
                        fontFamily="monospace"
                        fontWeight="bold"
                    >
                        by xDepredadorxD
                    </text>
                </svg>
            </div>
          </div>
        </div>
    );
};
