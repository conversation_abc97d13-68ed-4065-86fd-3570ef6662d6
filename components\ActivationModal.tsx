import React, { useState } from 'react';

interface ActivationModalProps {
    machineId: string;
    onActivationSuccess: (key: string) => void;
    t: (key: string) => string;
    verifyKey: (machineId: string, activationKey: string) => Promise<boolean>;
}

export const ActivationModal: React.FC<ActivationModalProps> = ({ machineId, onActivationSuccess, t, verifyKey }) => {
    const [key, setKey] = useState('');
    const [error, setError] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);

    const handleKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        // Solo permitir letras y números, máximo 12 caracteres
        const cleaned = e.target.value.replace(/[^A-Z0-9]/g, '').substring(0, 12);
        setKey(cleaned.toUpperCase());
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (key.length !== 12 || isVerifying) return;

        setIsVerifying(true);
        setError('');

        try {
            // Verificar la clave dos veces para asegurar consistencia
            const isValid1 = await verifyKey(machineId, key);
            const isValid2 = await verifyKey(machineId, key);

            if (isValid1 && isValid2) {
                // Verificación adicional: asegurar que la clave funciona después de almacenarla
                localStorage.setItem('activationKey', key);
                const finalCheck = await verifyKey(machineId, key);

                if (finalCheck) {
                    onActivationSuccess(key);
                } else {
                    localStorage.removeItem('activationKey');
                    setError(t('activation_invalid_key'));
                    setIsVerifying(false);
                }
            } else {
                setError(t('activation_invalid_key'));
                setIsVerifying(false);
            }
        } catch (error) {
            console.error('Error during activation verification:', error);
            setError(t('activation_invalid_key'));
            setIsVerifying(false);
        }
    };
    
    return (
        <div className="fixed inset-0 bg-slate-800 flex items-center justify-center z-50 p-4 font-sans">
          <div className="w-full max-w-md mx-auto">
            {/* Contenedor principal minimalista */}
            <div className="bg-slate-700/80 backdrop-blur-sm p-8 rounded-2xl shadow-2xl border border-slate-600/50">

              {/* Header simple */}
              <div className="text-center mb-8">
                <h1 className="text-3xl font-normal text-blue-400 mb-4">{t('activation_title')}</h1>
                <p className="text-slate-300 text-base leading-relaxed">{t('activation_description')}</p>
              </div>

              {/* Sección ID de Máquina */}
              <div className="mb-6">
                <label className="block text-slate-300 font-medium mb-3">{t('activation_mac_label')}</label>
                <div className="bg-slate-800/60 p-4 rounded-lg border border-slate-600/30">
                  <div className="font-mono text-slate-200 text-sm tracking-wide break-all select-all text-center">
                    {machineId}
                  </div>
                </div>
              </div>

              {/* Formulario de activación */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="activation-key" className="block text-slate-300 font-medium mb-3">{t('activation_key_label')}</label>
                  <input
                    id="activation-key"
                    type="text"
                    value={key}
                    onChange={handleKeyChange}
                    maxLength={12}
                    className="w-full bg-slate-800/60 p-4 rounded-lg border border-slate-600/30 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-center font-mono text-lg text-slate-200 uppercase tracking-widest placeholder-slate-500 transition-all duration-200"
                    disabled={isVerifying}
                    autoFocus
                    placeholder="————————————"
                  />
                </div>

                {error && (
                  <div className="bg-red-900/30 border border-red-700/30 rounded-lg p-3 text-center">
                    <p className="text-red-300 text-sm">{error}</p>
                  </div>
                )}

                <button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isVerifying || key.length !== 12}
                >
                  {isVerifying ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>{t('activation_verifying')}</span>
                    </div>
                  ) : (
                    t('activation_button')
                  )}
                </button>
              </form>

              {/* Footer simple */}
              <div className="mt-8 pt-6 text-center">
                <p className="text-blue-400 text-sm font-medium">
                  by <span className="text-blue-300">xDepredadorxD</span>
                </p>
              </div>

            </div>
          </div>
        </div>
    );
};
