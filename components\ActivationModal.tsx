import React, { useState } from 'react';

interface ActivationModalProps {
    machineId: string;
    onActivationSuccess: (key: string) => void;
    t: (key: string) => string;
    verifyKey: (machineId: string, activationKey: string) => Promise<boolean>;
}

export const ActivationModal: React.FC<ActivationModalProps> = ({ machineId, onActivationSuccess, t, verifyKey }) => {
    const [key, setKey] = useState('');
    const [error, setError] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);

    // Función para formatear la clave con guiones
    const formatKey = (value: string) => {
        // Remover todo lo que no sean letras y números
        const cleaned = value.replace(/[^A-Z0-9]/g, '');
        // Limitar a 12 caracteres
        const limited = cleaned.substring(0, 12);
        // Agregar guiones cada 4 caracteres
        return limited.replace(/(.{4})/g, '$1-').replace(/-$/, '');
    };

    const handleKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const formatted = formatKey(e.target.value.toUpperCase());
        setKey(formatted);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        // Remover guiones para verificar longitud
        const cleanKey = key.replace(/-/g, '');
        if (cleanKey.length !== 12 || isVerifying) return;

        setIsVerifying(true);
        setError('');

        try {
            // Verificar la clave dos veces para asegurar consistencia (sin guiones)
            const isValid1 = await verifyKey(machineId, cleanKey);
            const isValid2 = await verifyKey(machineId, cleanKey);

            if (isValid1 && isValid2) {
                // Verificación adicional: asegurar que la clave funciona después de almacenarla
                localStorage.setItem('activationKey', cleanKey);
                const finalCheck = await verifyKey(machineId, cleanKey);

                if (finalCheck) {
                    onActivationSuccess(cleanKey);
                } else {
                    localStorage.removeItem('activationKey');
                    setError(t('activation_invalid_key'));
                    setIsVerifying(false);
                }
            } else {
                setError(t('activation_invalid_key'));
                setIsVerifying(false);
            }
        } catch (error) {
            console.error('Error during activation verification:', error);
            setError(t('activation_invalid_key'));
            setIsVerifying(false);
        }
    };
    
    return (
        <div className="fixed inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 flex items-center justify-center z-50 p-4 font-sans">
          <div className="w-full max-w-lg mx-auto">
            {/* Contenedor principal con diseño profesional */}
            <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-8 rounded-3xl shadow-2xl border border-blue-400/30 backdrop-blur-sm">

              {/* Header con logo/icono */}
              <div className="text-center mb-8">
                <div className="w-20 h-20 mx-auto mb-4 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-0.257-0.257A6 6 0 1118 8zM2 8a6 6 0 1010.743 5.743L12 14l-0.257-0.257A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <h1 className="text-4xl font-bold text-white mb-3 tracking-tight">{t('activation_title')}</h1>
                <p className="text-blue-100 text-lg leading-relaxed max-w-md mx-auto">{t('activation_description')}</p>
              </div>

              {/* Sección ID de Máquina */}
              <div className="mb-8">
                <label className="block text-white font-semibold mb-3 text-lg">{t('activation_mac_label')}</label>
                <div className="bg-white/10 backdrop-blur-sm p-4 rounded-xl border border-white/20 text-center">
                  <div className="font-mono text-white text-lg tracking-wider break-all select-all bg-black/20 p-3 rounded-lg">
                    {machineId}
                  </div>
                </div>
              </div>

              {/* Formulario de activación */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="activation-key" className="block text-white font-semibold mb-3 text-lg">{t('activation_key_label')}</label>
                  <input
                    id="activation-key"
                    type="text"
                    value={key}
                    onChange={handleKeyChange}
                    maxLength={14} // 12 caracteres + 2 guiones
                    className="w-full bg-white/10 backdrop-blur-sm p-4 rounded-xl border border-white/30 focus:ring-4 focus:ring-white/20 focus:border-white/50 text-center font-mono text-2xl text-white uppercase tracking-[0.2em] placeholder-white/50 transition-all duration-200"
                    disabled={isVerifying}
                    autoFocus
                    placeholder="XXXX-XXXX-XXXX"
                  />
                  <div className="mt-2 text-center">
                    <span className="text-blue-200 text-sm">{key.replace(/-/g, '').length}/12 caracteres</span>
                  </div>
                </div>

                {error && (
                  <div className="bg-red-500/20 border border-red-400/30 rounded-xl p-4 text-center">
                    <p className="text-red-200 font-medium">{error}</p>
                  </div>
                )}

                <button
                  type="submit"
                  className="w-full bg-white text-blue-800 font-bold py-4 px-6 rounded-xl text-lg transition-all duration-200 hover:bg-blue-50 hover:shadow-lg transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:hover:bg-white"
                  disabled={isVerifying || key.replace(/-/g, '').length !== 12}
                >
                  {isVerifying ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-5 h-5 border-2 border-blue-800/30 border-t-blue-800 rounded-full animate-spin"></div>
                      <span>{t('activation_verifying')}</span>
                    </div>
                  ) : (
                    t('activation_button')
                  )}
                </button>
              </form>

              {/* Footer profesional */}
              <div className="mt-8 pt-6 border-t border-white/20">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-blue-100 text-sm font-medium">Sistema de Activación Seguro</span>
                  </div>

                  {/* Autor con estilo profesional */}
                  <div className="bg-white/5 backdrop-blur-sm rounded-lg p-3 border border-white/10">
                    <p className="text-white/80 text-sm font-medium tracking-wide">
                      Desarrollado por{' '}
                      <span className="text-white font-bold bg-gradient-to-r from-blue-200 to-white bg-clip-text text-transparent">
                        xDepredadorxD
                      </span>
                    </p>
                  </div>

                  {/* Indicadores de seguridad */}
                  <div className="flex justify-center items-center space-x-4 mt-4 text-xs text-blue-200">
                    <div className="flex items-center space-x-1">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                      <span>Cifrado</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span>Verificado</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      <span>MAC ID</span>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
    );
};
