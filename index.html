<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Battle Strategy Creator</title>
    <script src="https://cdn.tailwindcss.com"></script>
<style>
body {
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
}

@keyframes number-color-cycle {
  0%, 100% { fill: white; }
  25% { fill: #22c55e; } /* green */
  50% { fill: #eab308; } /* yellow */
  75% { fill: #ef4444; } /* red */
}

.number-color-anim {
    animation: number-color-cycle 4s linear infinite;
}

@keyframes text-outline-cycle {
  0%, 100% {
    -webkit-text-stroke-color: var(--outline-color-1, white);
  }
  50% {
    -webkit-text-stroke-color: var(--outline-color-2, #000080);
  }
}

.text-outline-anim {
    animation: text-outline-cycle 2s linear infinite;
    -webkit-text-stroke-width: var(--outline-width, 1px);
    paint-order: stroke fill;
}

/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.2); /* gray-400 with 20% opacity */
  border-radius: 5px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.4);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.2) transparent;
}
</style>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-gray-800 text-white">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>