{"name": "strategy-creator-2.4", "private": true, "version": "0.0.0", "main": "main.js", "homepage": "./", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "electron": "cross-env NODE_ENV=production electron .", "electron-dev": "node electron-dev.js", "electron-dev-old": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .\"", "electron-dev-debug": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron . --inspect=5858\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "test-electron": "cross-env NODE_ENV=development electron ."}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0", "electron": "^33.2.1", "electron-builder": "^25.1.8", "concurrently": "^9.1.0", "wait-on": "^8.0.1", "cross-env": "^7.0.3"}, "build": {"appId": "com.depredador.strategy-creator", "productName": "Strategy Creator", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "main.js", "preload.js", "package.json"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}