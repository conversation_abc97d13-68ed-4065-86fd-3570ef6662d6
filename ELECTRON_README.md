# Strategy Creator - Electron Implementation

## Configuración para Electron

Este proyecto ahora incluye soporte completo para Electron con verificación de activación basada en la dirección MAC de la red.

### Características de Seguridad Implementadas

1. **Verificación por MAC Address**: La aplicación obtiene la dirección MAC real del equipo para generar un identificador único de máquina.

2. **Verificación Continua**: La aplicación verifica la validez de la clave de activación cada 30 segundos mientras está en ejecución.

3. **Seguridad Electron**: 
   - Context isolation habilitado
   - Node integration deshabilitado en el renderer
   - Preload script seguro para exponer solo las APIs necesarias

### Scripts Disponibles

```bash
# Desarrollo (ejecuta Vite + Electron)
npm run electron-dev

# Solo Electron (requiere build previo)
npm run electron

# Build para web
npm run build

# Build y empaquetado para Electron
npm run build-electron

# Crear distribución final
npm run dist
```

### Estructura de Archivos

- `main.js` - Proceso principal de Electron
- `preload.js` - Script de precarga seguro
- `App.tsx` - Aplicación React con lógica de activación actualizada

### Cómo Funciona la Activación

1. **Al iniciar la aplicación**:
   - Se obtiene la dirección MAC del equipo
   - Se verifica si existe una clave de activación almacenada
   - Si la clave es válida, se activa la aplicación

2. **Durante la ejecución**:
   - Cada 30 segundos se verifica que la clave siga siendo válida
   - Si la clave se vuelve inválida, se desactiva la aplicación automáticamente

3. **Generación de claves**:
   - Las claves se generan usando HMAC-SHA256 con la MAC address
   - Formato: 5 caracteres del inicio + 7 caracteres del final del hash

### Instalación de Dependencias

```bash
npm install
```

### Desarrollo

Para desarrollar con hot-reload:

```bash
npm run electron-dev
```

Esto iniciará tanto el servidor de desarrollo de Vite como Electron, con recarga automática cuando cambies el código.

### Construcción para Producción

```bash
npm run dist
```

Esto creará los instaladores para tu plataforma en la carpeta `dist-electron/`.

### Configuración de Build

La configuración de electron-builder está en `package.json` bajo la sección `build`. Puedes personalizar:

- Icono de la aplicación
- Nombre del producto
- Configuraciones específicas por plataforma
- Archivos a incluir/excluir

### Notas de Seguridad

- La aplicación previene navegación a URLs externas
- Solo se exponen las APIs necesarias al proceso de renderizado
- La verificación de activación es continua y robusta
- La dirección MAC se obtiene de forma segura desde el proceso principal

### Troubleshooting

Si tienes problemas:

1. Asegúrate de que todas las dependencias estén instaladas
2. Verifica que el puerto 5173 esté disponible para desarrollo
3. En caso de errores de permisos, ejecuta como administrador
4. Para debugging, las DevTools están disponibles en modo desarrollo
